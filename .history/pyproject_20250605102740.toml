[project]
name = "motion-agent"
version = "0.1.0"
description = "Natural Language to 3D Animation Generation System"
readme = "README.md"
requires-python = ">=3.11"
authors = [{ name = "BigFaceMaster", email = "<EMAIL>" }]
dependencies = [
  "fastapi>=0.104.1",
  "uvicorn[standard]>=0.24.0",
  "spacy>=3.5",
  "transformers>=4.30",
  "pydantic>=2.5.0",
  "torch>=2.0",
  "haystack>=1.20",
  "python-multipart>=0.0.6",
  "nltk>=3.8.1",
  "numpy>=1.24.3",
  "requests>=2.31.0",
  "python-dotenv>=1.0.0",
  "aiofiles>=23.2.1",
  "jinja2>=3.1.2",
  "loguru>=0.7.2",
  "scipy>=1.11.0",
  "matplotlib>=3.7.0",
  "opencv-python>=4.8.0",
  "langchain>=0.1.0",
  "langchain-core>=0.1.0",
  "langchain-community>=0.0.10",
  "langgraph>=0.0.20",
]

[project.optional-dependencies]
blender = ["bpy>=4.0.0"]

[dependency-groups]
dev = [
  "pytest>=7.4.3",
  "pytest-asyncio>=0.21.1",
  "black>=23.11.0",
  "flake8>=6.1.0",
  "mypy>=1.7.1",
  "pre-commit>=3.5.0",
  "ruff>=0.1.8",
]

[project.scripts]
start-server = "backend.app:main"
lint = "ruff check backend/"
format = "ruff format backend/"
lint-fix = "ruff check --fix backend/"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"

[tool.ruff]
# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.8+
target-version = "py38"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = [
  "E4",  # pycodestyle errors
  "E7",  # pycodestyle errors
  "E9",  # pycodestyle errors
  "F",   # Pyflakes
  "W",   # pycodestyle warnings
  "I",   # isort
  "N",   # pep8-naming
  "UP",  # pyupgrade
  "B",   # flake8-bugbear
  "C4",  # flake8-comprehensions
  "SIM", # flake8-simplify
  "TCH", # flake8-type-checking
]
ignore = [
  "E501", # Line too long (handled by Black)
  "B008", # Do not perform function calls in argument defaults
  "B904", # Allow `raise` without `from` inside `except`
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
